import { GoogleGenerativeA<PERSON> } from "@google/generative-ai"
import { configStore } from "./config"
import { <PERSON><PERSON><PERSON><PERSON>, MCPToolCall, LLMToolCallResponse, MCPToolResult } from "./mcp-service"
import { AgentProgressStep, AgentProgressUpdate } from "../shared/types"
import { getRendererHandlers } from "@egoist/tipc/main"
import { WINDOWS, showPanelWindow } from "./window"
import { RendererHandlers } from "./renderer-handlers"
import { diagnosticsService } from "./diagnostics"
import {
  makeStructuredToolCall,
  makeStructuredContextExtraction,
  makeTextCompletion
} from "./structured-output"

/**
 * Use LLM to extract useful context from conversation history
 */
async function extractContextFromHistory(
  conversationHistory: Array<{
    role: "user" | "assistant" | "tool"
    content: string
    toolCalls?: MCPToolCall[]
    toolResults?: MCPToolResult[]
  }>,
  config: any
): Promise<{ contextSummary: string; resources: Array<{ type: string; id: string; parameter: string }> }> {
  if (conversationHistory.length === 0) {
    return { contextSummary: "", resources: [] }
  }

  // Create a condensed version of the conversation for analysis
  const conversationText = conversationHistory.map(entry => {
    let text = `${entry.role.toUpperCase()}: ${entry.content}`

    if (entry.toolCalls) {
      text += `\nTOOL_CALLS: ${entry.toolCalls.map(tc => `${tc.name}(${JSON.stringify(tc.arguments)})`).join(', ')}`
    }

    if (entry.toolResults) {
      text += `\nTOOL_RESULTS: ${entry.toolResults.map(tr => tr.isError ? 'ERROR' : 'SUCCESS').join(', ')}`
    }

    return text
  }).join('\n\n')

  const contextExtractionPrompt = `Analyze the following conversation history and extract useful context information that would be needed for continuing the conversation.

CONVERSATION HISTORY:
${conversationText}

Your task is to identify and extract:
1. Resource identifiers (session IDs, connection IDs, file handles, workspace IDs, etc.)
2. Important file paths or locations mentioned
3. Current state or status information
4. Any other context that would be useful for subsequent tool calls

Respond with a JSON object in this exact format:
{
  "contextSummary": "Brief summary of the current state and what has been accomplished",
  "resources": [
    {
      "type": "session|connection|handle|workspace|channel|other",
      "id": "the actual ID value",
      "parameter": "the parameter name this ID should be used for (e.g., sessionId, connectionId)"
    }
  ]
}

Focus on extracting actual resource identifiers that tools would need, not just mentioning them.
Only include resources that are currently active and usable.
Keep the contextSummary concise but informative.`

  try {
    console.log(`[CONTEXT-EXTRACTION] 🚀 Using structured output for context extraction`)
    const result = await makeStructuredContextExtraction(contextExtractionPrompt, config.mcpToolsProviderId)
    console.log(`[CONTEXT-EXTRACTION] ✅ Extracted context:`, result)
    return result
  } catch (error) {
    console.log(`[CONTEXT-EXTRACTION] ❌ Error during context extraction:`, error)
    return { contextSummary: "", resources: [] }
  }
}

/**
 * Extract the actual answer from tool results based on the user's question
 */
function extractAnswerFromToolResult(toolResult: string, userQuestion: string): string {
  const lowerQuestion = userQuestion.toLowerCase()
  const lowerResult = toolResult.toLowerCase()

  // For CPU/process questions, extract the top process information
  if (lowerQuestion.includes('cpu') || lowerQuestion.includes('process')) {
    // Look for process information patterns
    const lines = toolResult.split('\n')
    const processLines = lines.filter(line => {
      const lowerLine = line.toLowerCase()
      return lowerLine.includes('%') || lowerLine.includes('cpu') || lowerLine.includes('pid')
    })

    if (processLines.length > 0) {
      // Return the most relevant process information
      return `The process using the most CPU is:\n${processLines.slice(0, 3).join('\n')}`
    }
  }

  // For email questions, extract email information
  if (lowerQuestion.includes('email') || lowerQuestion.includes('mail')) {
    const lines = toolResult.split('\n')
    const emailLines = lines.filter(line =>
      line.includes('From:') || line.includes('Subject:') || line.includes('@')
    )

    if (emailLines.length > 0) {
      return emailLines.slice(0, 5).join('\n')
    }
  }

  // For file/directory questions, extract relevant file information
  if (lowerQuestion.includes('file') || lowerQuestion.includes('directory')) {
    const lines = toolResult.split('\n')
    const fileLines = lines.filter(line =>
      line.includes('/') || line.includes('.') || /\d+\s+(files?|directories?)/.test(line)
    )

    if (fileLines.length > 0) {
      return fileLines.slice(0, 10).join('\n')
    }
  }

  // Default: return the tool result as-is, but clean it up
  return toolResult.trim()
}

/**
 * Analyze tool errors and provide recovery strategies
 */
function analyzeToolErrors(
  toolResults: MCPToolResult[],
  failedTools: string[],
  toolCalls: MCPToolCall[]
): { recoveryStrategy: string; errorTypes: string[] } {
  const errorTypes: string[] = []
  const errorMessages = toolResults
    .filter(r => r.isError)
    .map(r => r.content.map(c => c.text).join(' '))
    .join(' ')

  // Categorize error types
  if (errorMessages.includes('Session not found')) {
    errorTypes.push('session_lost')
  }
  if (errorMessages.includes('timeout') || errorMessages.includes('connection')) {
    errorTypes.push('connectivity')
  }
  if (errorMessages.includes('permission') || errorMessages.includes('access')) {
    errorTypes.push('permissions')
  }
  if (errorMessages.includes('not found') || errorMessages.includes('does not exist')) {
    errorTypes.push('resource_missing')
  }

  // Generate recovery strategy based on error types
  let recoveryStrategy = 'RECOVERY STRATEGIES:\n'

  if (errorTypes.includes('session_lost')) {
    recoveryStrategy += '- For session errors: Create a new session using ht_create_session first\n'
  }
  if (errorTypes.includes('connectivity')) {
    recoveryStrategy += '- For connectivity issues: Wait a moment and retry, or check if the service is running\n'
  }
  if (errorTypes.includes('permissions')) {
    recoveryStrategy += '- For permission errors: Try alternative file locations or check access rights\n'
  }
  if (errorTypes.includes('resource_missing')) {
    recoveryStrategy += '- For missing resources: Verify the resource exists or create it first\n'
  }

  if (errorTypes.length === 0) {
    recoveryStrategy += '- General: Try breaking down the task into smaller steps or use alternative tools\n'
  }

  return { recoveryStrategy, errorTypes }
}





export async function postProcessTranscript(transcript: string) {
  const config = configStore.get()

  if (
    !config.transcriptPostProcessingEnabled ||
    !config.transcriptPostProcessingPrompt
  ) {
    return transcript
  }

  const prompt = config.transcriptPostProcessingPrompt.replace(
    "{transcript}",
    transcript,
  )

  const chatProviderId = config.transcriptPostProcessingProviderId

  if (chatProviderId === "gemini") {
    if (!config.geminiApiKey) throw new Error("Gemini API key is required")

    const gai = new GoogleGenerativeAI(config.geminiApiKey)
    const geminiModel = config.transcriptPostProcessingGeminiModel || "gemini-1.5-flash-002"
    const gModel = gai.getGenerativeModel({ model: geminiModel })

    const result = await gModel.generateContent([prompt], {
      baseUrl: config.geminiBaseUrl,
    })
    return result.response.text().trim()
  }

  // Use structured output service for OpenAI/Groq providers
  try {
    console.log(`[TRANSCRIPT-PROCESSING] 🚀 Using structured output service`)
    const result = await makeTextCompletion(prompt, chatProviderId)
    return result
  } catch (error) {
    console.error(`[TRANSCRIPT-PROCESSING] ❌ Error:`, error)
    throw error
  }
}

export async function processTranscriptWithTools(
  transcript: string,
  availableTools: MCPTool[]
): Promise<LLMToolCallResponse> {
  const config = configStore.get()

  if (!config.mcpToolsEnabled) {
    return { content: transcript }
  }

  // Create system prompt with available tools
  const baseSystemPrompt = config.mcpToolsSystemPrompt || `You are a helpful assistant that can execute tools based on user requests.`

  // Generate dynamic tool information including schemas
  const generateToolInfo = (tools: MCPTool[]) => {
    return tools.map(tool => {
      let info = `- ${tool.name}: ${tool.description}`

      // Add parameter schema if available
      if (tool.inputSchema && tool.inputSchema.properties) {
        const params = Object.entries(tool.inputSchema.properties)
          .map(([key, schema]: [string, any]) => {
            const type = schema.type || 'any'
            const required = tool.inputSchema.required?.includes(key) ? ' (required)' : ''
            return `${key}: ${type}${required}`
          })
          .join(', ')

        if (params) {
          info += `\n  Parameters: {${params}}`
        }
      }

      return info
    }).join('\n')
  }

  // Remove duplicates from available tools to prevent confusion
  const uniqueAvailableTools = availableTools.filter((tool, index, self) =>
    index === self.findIndex(t => t.name === tool.name)
  )

  // Always inject available tools into the system prompt
  const toolsList = uniqueAvailableTools.length > 0 ? `

Available tools:
${generateToolInfo(uniqueAvailableTools)}` : '\n\nNo tools are currently available.'

  const systemPrompt = baseSystemPrompt + toolsList + `

IMPORTANT: You must respond with ONLY a valid JSON object. Do not include any explanatory text before or after the JSON.

CRITICAL: When calling tools, you MUST use the EXACT tool name as listed above, including any server prefixes (like "server:tool_name"). Do not modify or shorten the tool names. NEVER invent or hallucinate tool names that are not in the list above.

TOOL USAGE PATTERNS:
- Use the exact tool names as listed above, including any server prefixes
- Follow the parameter schemas provided by each tool's inputSchema
- When in doubt about parameters, prefer camelCase over snake_case naming

PARAMETER NAMING GUIDELINES:
- Different MCP servers may use different parameter naming conventions
- Common patterns: camelCase (sessionId), snake_case (session_id), kebab-case (session-id)
- If a tool call fails due to parameter naming, the system will automatically retry with corrected parameters
- Always use the exact parameter names specified in the tool's schema

ALWAYS prefer using available tools over suggesting manual approaches. If you can accomplish the task with the available tools, do it!

When the user's request requires using a tool, respond with this exact JSON format:
{
  "toolCalls": [
    {
      "name": "exact_tool_name_from_list_above",
      "arguments": { "param1": "value1", "param2": "value2" }
    }
  ],
  "content": "Brief explanation of what you're doing"
}

If no tools are needed, respond with this exact JSON format:
{
  "content": "Your response text here"
}

CRITICAL JSON FORMATTING RULES:
- Always escape special characters in JSON strings (newlines as \\n, quotes as \\\", backslashes as \\\\)
- Never include unescaped newlines, tabs, or control characters in JSON string values
- If you need to include multi-line text, use \\n for line breaks
- Always use double quotes for JSON strings, never single quotes
- Ensure all JSON is properly closed with matching braces and brackets

Examples:

User: "List the contents of my desktop"
Response:
{
  "toolCalls": [
    {
      "name": "Headless Terminal:ht_create_session",
      "arguments": {}
    }
  ],
  "content": "Creating a terminal session to list your desktop contents"
}

Follow-up after session created (sessionId: "abc-123"):
{
  "toolCalls": [
    {
      "name": "Headless Terminal:ht_execute_command",
      "arguments": {
        "sessionId": "abc-123",
        "command": "ls ~/Desktop"
      }
    }
  ],
  "content": "Listing desktop contents"
}

User: "What's the weather like?"
Response:
{
  "content": "I don't have access to weather information. You might want to check a weather app or website."
}

Remember: Respond with ONLY the JSON object, no markdown formatting, no code blocks, no additional text.`

  const messages = [
    {
      role: "system",
      content: systemPrompt
    },
    {
      role: "user",
      content: transcript
    }
  ]

  const chatProviderId = config.mcpToolsProviderId

  // Debug: Log non-agent mode LLM call details
  console.log("[MCP-TOOLS-DEBUG] 🚀 Making non-agent LLM call")
  console.log("[MCP-TOOLS-DEBUG] 🔧 Provider:", chatProviderId || "openai (default)")
  console.log("[MCP-TOOLS-DEBUG] 📝 Messages count:", messages.length)
  messages.forEach((msg, i) => {
    const preview = msg.content.length > 200 ? msg.content.substring(0, 200) + "..." : msg.content
    console.log(`[MCP-TOOLS-DEBUG]   ${i + 1}. ${msg.role}: ${preview}`)
  })

  if (chatProviderId === "gemini") {
    const geminiModel = config.mcpToolsGeminiModel || "gemini-1.5-flash-002"
    console.log("[MCP-TOOLS-DEBUG] 🤖 Using Gemini model:", geminiModel)
    if (!config.geminiApiKey) throw new Error("Gemini API key is required")

    const gai = new GoogleGenerativeAI(config.geminiApiKey)
    const gModel = gai.getGenerativeModel({ model: geminiModel })

    const prompt = messages.map(m => `${m.role}: ${m.content}`).join('\n\n')
    console.log("[MCP-TOOLS-DEBUG] 📤 Sending request to Gemini...")

    const result = await gModel.generateContent([prompt], {
      baseUrl: config.geminiBaseUrl,
    })

    const responseText = result.response.text().trim()
    console.log(`[MCP-DEBUG] Gemini response:`, responseText)

    // For Gemini, we still need to parse manually since it doesn't support structured output
    try {
      const parsed = JSON.parse(responseText)
      if (parsed && (parsed.toolCalls || parsed.content)) {
        console.log(`[MCP-DEBUG] ✅ Successfully parsed Gemini JSON response:`, parsed)
        return parsed
      }
    } catch (parseError) {
      console.log(`[MCP-DEBUG] ⚠️ Failed to parse Gemini response as JSON, returning as content`)
    }
    return { content: responseText }
  }

  // Use structured output for OpenAI/Groq providers
  try {
    console.log(`[MCP-TOOLS-DEBUG] 🚀 Using structured output for tool calls`)
    const result = await makeStructuredToolCall(messages, chatProviderId)
    console.log(`[MCP-DEBUG] ✅ Successfully processed with structured output:`, result)
    return result
  } catch (error) {
    console.error(`[MCP-TOOLS-DEBUG] ❌ Structured output failed:`, error)
    throw error
  }
}

export interface AgentModeResponse {
  content: string
  conversationHistory: Array<{
    role: "user" | "assistant" | "tool"
    content: string
    toolCalls?: MCPToolCall[]
    toolResults?: MCPToolResult[]
  }>
  totalIterations: number
}

// Helper function to emit progress updates to the renderer
function emitAgentProgress(update: AgentProgressUpdate) {
  const panel = WINDOWS.get("panel")
  if (!panel) {
    return
  }

  // Show the panel window if it's not visible
  if (!panel.isVisible()) {
    showPanelWindow()
  }

  try {
    const handlers = getRendererHandlers<RendererHandlers>(panel.webContents)
    if (!handlers.agentProgressUpdate) {
      return
    }

    handlers.agentProgressUpdate.send(update)
  } catch (error) {
    console.error("Failed to emit progress update:", error)
  }
}

// Helper function to create progress steps
function createProgressStep(
  type: AgentProgressStep["type"],
  title: string,
  description?: string,
  status: AgentProgressStep["status"] = "pending"
): AgentProgressStep {
  return {
    id: `step_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    type,
    title,
    description,
    status,
    timestamp: Date.now()
  }
}

// Helper function to analyze tool capabilities and match them to user requests
function analyzeToolCapabilities(availableTools: MCPTool[], transcript: string): { summary: string; relevantTools: MCPTool[] } {
  const transcriptLower = transcript.toLowerCase()
  const relevantTools: MCPTool[] = []

  // Define capability patterns based on common keywords and tool descriptions
  const patterns = {
    filesystem: {
      keywords: ['file', 'directory', 'folder', 'desktop', 'list', 'ls', 'contents', 'browse', 'create', 'write', 'read'],
      toolDescriptionKeywords: ['file', 'directory', 'folder', 'filesystem', 'path', 'create', 'write', 'read', 'list']
    },
    terminal: {
      keywords: ['command', 'execute', 'run', 'terminal', 'shell', 'bash', 'script'],
      toolDescriptionKeywords: ['command', 'execute', 'terminal', 'shell', 'session', 'run']
    },
    system: {
      keywords: ['system', 'process', 'status', 'info', 'monitor', 'snapshot'],
      toolDescriptionKeywords: ['system', 'process', 'status', 'monitor', 'snapshot', 'info']
    },
    web: {
      keywords: ['web', 'http', 'api', 'request', 'url', 'fetch', 'search'],
      toolDescriptionKeywords: ['web', 'http', 'api', 'request', 'url', 'fetch', 'search', 'browser']
    },
    communication: {
      keywords: ['send', 'message', 'email', 'notification', 'slack', 'discord'],
      toolDescriptionKeywords: ['send', 'message', 'email', 'notification', 'slack', 'discord', 'communicate']
    }
  }

  // Check which patterns match the transcript
  const matchedCapabilities: string[] = []

  for (const [capability, pattern] of Object.entries(patterns)) {
    const hasKeyword = pattern.keywords.some(keyword => transcriptLower.includes(keyword))

    // Find tools that match this capability based on their descriptions
    const capabilityTools = availableTools.filter(tool => {
      const toolNameLower = tool.name.toLowerCase()
      const toolDescLower = tool.description.toLowerCase()

      return pattern.toolDescriptionKeywords.some(keyword =>
        toolNameLower.includes(keyword) || toolDescLower.includes(keyword)
      )
    })

    if (hasKeyword && capabilityTools.length > 0) {
      matchedCapabilities.push(capability)
      relevantTools.push(...capabilityTools)
    }
  }

  let summary = ""
  if (matchedCapabilities.length > 0) {
    summary = `Detected ${matchedCapabilities.join(', ')} capabilities. Can help with this request using available tools.`
  } else {
    summary = "Analyzing available tools for potential solutions."
  }

  // Remove duplicates from relevant tools
  const uniqueRelevantTools = relevantTools.filter((tool, index, self) =>
    index === self.findIndex(t => t.name === tool.name)
  )

  return { summary, relevantTools: uniqueRelevantTools }
}

export async function processTranscriptWithAgentMode(
  transcript: string,
  availableTools: MCPTool[],
  executeToolCall: (toolCall: MCPToolCall) => Promise<MCPToolResult>,
  maxIterations: number = 10,
  previousConversationHistory?: Array<{
    role: "user" | "assistant" | "tool"
    content: string
    toolCalls?: MCPToolCall[]
    toolResults?: MCPToolResult[]
  }>
): Promise<AgentModeResponse> {
  const config = configStore.get()

  if (!config.mcpToolsEnabled || !config.mcpAgentModeEnabled) {
    const fallbackResponse = await processTranscriptWithTools(transcript, availableTools)
    return {
      content: fallbackResponse.content || "",
      conversationHistory: [
        { role: "user", content: transcript },
        { role: "assistant", content: fallbackResponse.content || "" }
      ],
      totalIterations: 1
    }
  }

  console.log("[MCP-AGENT] 🤖 Starting agent mode processing...")

  // Initialize progress tracking
  const progressSteps: AgentProgressStep[] = []

  // Add initial step
  const initialStep = createProgressStep("thinking", "Analyzing request", "Processing your request and determining next steps", "in_progress")
  progressSteps.push(initialStep)

  // Analyze available tool capabilities
  const toolCapabilities = analyzeToolCapabilities(availableTools, transcript)

  // Update initial step with tool analysis
  initialStep.status = "completed"
  initialStep.description = `Found ${availableTools.length} available tools. ${toolCapabilities.summary}`

  // Emit initial progress
  emitAgentProgress({
    currentIteration: 0,
    maxIterations,
    steps: progressSteps.slice(-3), // Show max 3 steps
    isComplete: false
  })

  // Enhanced system prompt for agent mode
  const baseSystemPrompt = config.mcpToolsSystemPrompt || `You are a helpful assistant that can execute tools based on user requests. You are operating in agent mode, which means you can see the results of tool executions and make follow-up tool calls as needed.

CRITICAL RULES:
1. You MUST provide the actual answer to the user's question, not just describe what you're doing
2. If tools fail, you MUST retry with alternative approaches until you get the actual data
3. Never complete a task without providing the real answer the user requested
4. If you create sessions or setup tools, you MUST follow through to get the actual data
5. Session creation is just setup - you need to execute commands to get real results
6. If a command fails, try alternative commands (e.g., if "top -b" fails, try "ps aux" or "htop")
7. Always verify that your tool results contain the actual data before completing

For example:
- If asked "what process is using most CPU", you must return the actual process name and CPU usage percentage
- If asked about files, you must return the actual file names and details
- If asked about emails, you must return the actual email subjects and senders
- If asked about system info, you must return the actual system statistics

COMPLETION CRITERIA:
- Only mark needsMoreWork=false when you have the ACTUAL answer to provide
- Tool setup (like session creation) is NOT completion - it's just preparation
- You must execute commands and get real data before completing
- If you only have session IDs or "created successfully" messages, you need to continue working
- The final response must contain the specific data the user requested

CONTEXT AWARENESS:
- Maintain awareness of files created, modified, or referenced in previous operations
- When asked to read "the file" or "that file", refer to the most recently created or mentioned file
- Remember session IDs from terminal operations to reuse them when appropriate
- Build upon previous actions rather than starting from scratch`

  // Generate dynamic tool information including schemas
  const generateToolInfo = (tools: MCPTool[]) => {
    return tools.map(tool => {
      let info = `- ${tool.name}: ${tool.description}`

      // Add parameter schema if available
      if (tool.inputSchema && tool.inputSchema.properties) {
        const params = Object.entries(tool.inputSchema.properties)
          .map(([key, schema]: [string, any]) => {
            const type = schema.type || 'any'
            const required = tool.inputSchema.required?.includes(key) ? ' (required)' : ''
            return `${key}: ${type}${required}`
          })
          .join(', ')

        if (params) {
          info += `\n  Parameters: {${params}}`
        }
      }

      return info
    }).join('\n')
  }

  // Remove duplicates from available tools to prevent confusion
  const uniqueAvailableTools = availableTools.filter((tool, index, self) =>
    index === self.findIndex(t => t.name === tool.name)
  )

  // Always inject available tools into the system prompt
  const toolsList = uniqueAvailableTools.length > 0 ? `

Available tools:
${generateToolInfo(uniqueAvailableTools)}

${toolCapabilities.relevantTools.length > 0 && toolCapabilities.relevantTools.length < uniqueAvailableTools.length ? `
MOST RELEVANT TOOLS FOR THIS REQUEST:
${generateToolInfo(toolCapabilities.relevantTools)}
` : ''}` : '\n\nNo tools are currently available.'

  const systemPrompt = baseSystemPrompt + toolsList + `

IMPORTANT: You must respond with ONLY a valid JSON object. Do not include any explanatory text before or after the JSON.

CRITICAL: When calling tools, you MUST use the EXACT tool name as listed above, including any server prefixes (like "server:tool_name"). Do not modify or shorten the tool names. NEVER invent or hallucinate tool names that are not in the list above.

TOOL USAGE PATTERNS:
- Use the exact tool names as listed above, including any server prefixes
- Follow the parameter schemas provided by each tool's inputSchema
- When in doubt about parameters, prefer camelCase over snake_case naming

PARAMETER NAMING GUIDELINES:
- Different MCP servers may use different parameter naming conventions
- Common patterns: camelCase (sessionId), snake_case (session_id), kebab-case (session-id)
- If a tool call fails due to parameter naming, the system will automatically retry with corrected parameters
- Always use the exact parameter names specified in the tool's schema

ALWAYS prefer using available tools over suggesting manual approaches. If you can accomplish the task with the available tools, do it!

In agent mode, you can:
1. Execute tools and see their results
2. Make follow-up tool calls based on the results
3. Continue until the task is complete

When you need to use tools and expect to continue working after seeing the results, respond with:
{
  "toolCalls": [
    {
      "name": "exact_tool_name_from_list_above",
      "arguments": { "param1": "value1", "param2": "value2" }
    }
  ],
  "content": "Brief explanation of what you're doing",
  "needsMoreWork": true
}

When you need to use tools and the task will be complete after executing them, respond with:
{
  "toolCalls": [
    {
      "name": "exact_tool_name_from_list_above",
      "arguments": { "param1": "value1", "param2": "value2" }
    }
  ],
  "content": "Brief explanation of what you're doing",
  "needsMoreWork": false
}

When the task is complete and no more tools are needed, respond with:
{
  "content": "Final response with task completion summary",
  "needsMoreWork": false
}

If no tools are needed for the initial request, respond with:
{
  "content": "Your response text here",
  "needsMoreWork": false
}

Remember: Respond with ONLY the JSON object, no markdown formatting, no code blocks, no additional text.

CRITICAL JSON FORMATTING RULES:
- Always escape special characters in JSON strings (newlines as \\n, quotes as \\\", backslashes as \\\\)
- Never include unescaped newlines, tabs, or control characters in JSON string values
- If you need to include multi-line text or commands, use \\n for line breaks
- Always use double quotes for JSON strings, never single quotes
- Ensure all JSON is properly closed with matching braces and brackets
- Test your JSON mentally before responding to ensure it's valid`

  // Debug: Log the system prompt being used
  // console.log("[MCP-AGENT-DEBUG] 📝 Using custom system prompt:", !!config.mcpToolsSystemPrompt)
  // console.log("[MCP-AGENT-DEBUG] 📝 Full system prompt:")
  // console.log(systemPrompt)
  // console.log("[MCP-AGENT-DEBUG] 📝 System prompt length:", systemPrompt.length)
  console.log("[MCP-AGENT-DEBUG] 🔧 Available tools:", availableTools.map(t => t.name).join(", "))
  console.log("[MCP-AGENT-DEBUG] 🎯 Tool capabilities:", toolCapabilities.summary)

  // Generic context extraction from chat history - works with any MCP tool
  const extractRecentContext = (history: Array<{ role: string; content: string; toolCalls?: any[]; toolResults?: any[] }>) => {
    // Simply return the recent conversation history - let the LLM understand the context
    // This is much simpler and works with any MCP tool, not just specific ones
    return history.slice(-8) // Last 8 messages provide sufficient context
  }





  const conversationHistory: Array<{
    role: "user" | "assistant" | "tool"
    content: string
    toolCalls?: MCPToolCall[]
    toolResults?: MCPToolResult[]
  }> = [
    ...(previousConversationHistory || []),
    { role: "user", content: transcript }
  ]

  // Get recent context for the LLM - no specific extraction needed
  const recentContext = extractRecentContext(conversationHistory)

  // Log context for debugging
  if (previousConversationHistory) {
    console.log(`[MCP-AGENT-DEBUG] 📚 Loaded ${previousConversationHistory.length} previous messages from conversation`)
    console.log(`[MCP-AGENT-DEBUG] 📋 Using ${recentContext.length} recent messages for context`)
  }

  let iteration = 0
  let finalContent = ""

  while (iteration < maxIterations) {
    iteration++
    console.log(`[MCP-AGENT] 🔄 Agent iteration ${iteration}/${maxIterations}`)

    // Update initial step to completed and add thinking step for this iteration
    if (iteration === 1) {
      initialStep.status = "completed"
    }

    const thinkingStep = createProgressStep(
      "thinking",
      `Planning step ${iteration}`,
      "Analyzing context and determining next actions",
      "in_progress"
    )
    progressSteps.push(thinkingStep)

    // Emit progress update
    emitAgentProgress({
      currentIteration: iteration,
      maxIterations,
      steps: progressSteps.slice(-3),
      isComplete: false
    })

    // Use the base system prompt - let the LLM understand context from conversation history
    let contextAwarePrompt = systemPrompt

    // Add enhanced context instruction using LLM-based context extraction
    if (recentContext.length > 1) {
      // Use LLM to extract useful context from conversation history
      const contextInfo = await extractContextFromHistory(conversationHistory, config)

      contextAwarePrompt += `\n\nCONTEXT AWARENESS:
You have access to the recent conversation history. Use this history to understand:
- Any resources (sessions, files, connections, etc.) that were created or mentioned
- Previous tool calls and their results
- User preferences and workflow patterns
- Any ongoing tasks or processes

${contextInfo.contextSummary ? `
CURRENT CONTEXT:
${contextInfo.contextSummary}
` : ''}

${contextInfo.resources.length > 0 ? `
AVAILABLE RESOURCES:
${contextInfo.resources.map(r => `- ${r.type.toUpperCase()}: ${r.id} (use as parameter: ${r.parameter})`).join('\n')}

CRITICAL: When using tools that require resource IDs, you MUST use the exact resource IDs listed above.
DO NOT create fictional or made-up resource identifiers.
` : ''}

RESOURCE USAGE GUIDELINES:
- Always check the conversation history for existing resource IDs before creating new ones
- Use the exact resource ID values provided above
- Match the resource ID to the correct parameter name as specified
- If no suitable resource is available, create a new one using the appropriate creation tool first

NEVER invent resource IDs like "my-session-123" or "temp-connection-id".
Always use actual resource IDs from the conversation history or create new ones properly.`
    }

    // Build messages for LLM call
    const messages = [
      { role: "system", content: contextAwarePrompt },
      ...conversationHistory.map(entry => {
        if (entry.role === "tool") {
          return {
            role: "user" as const,
            content: `Tool execution results:\n${entry.content}`
          }
        }
        return {
          role: entry.role as "user" | "assistant",
          content: entry.content
        }
      })
    ]

    // Make LLM call
    console.log(`[MCP-AGENT-DEBUG] 🧠 Making LLM call for iteration ${iteration}`)
    const llmResponse = await makeLLMCall(messages, config)
    console.log(`[MCP-AGENT-DEBUG] 🎯 LLM response for iteration ${iteration}:`, JSON.stringify(llmResponse, null, 2))

    // Update thinking step to completed
    thinkingStep.status = "completed"

    // Check for completion signals - but be smarter about when task is actually complete
    const hasToolCalls = llmResponse.toolCalls && llmResponse.toolCalls.length > 0
    const agentSaysComplete = !hasToolCalls && (
      !llmResponse.toolCalls ||
      llmResponse.toolCalls.length === 0 ||
      (llmResponse as any).needsMoreWork === false
    )

    // Check if we actually have meaningful results that answer the user's question
    const hasActualResults = progressSteps.some(step => {
      if (!step.toolResult?.success || !step.toolResult.content) return false
      const content = step.toolResult.content.toLowerCase()
      return (
        // Has process/CPU data
        (content.includes('cpu') && content.includes('%')) ||
        (content.includes('pid') && content.includes('command')) ||
        // Has email data
        (content.includes('from:') || content.includes('subject:')) ||
        // Has file content (not just creation)
        (content.includes('content') && !content.includes('created')) ||
        // Has numeric results
        /\d+\s+(files?|items?|emails?|results?)/.test(content) ||
        // Has actual command output (not just session info)
        (content.includes('processes') || content.includes('top') || content.includes('ps'))
      )
    })

    // More strict completion logic - require actual results unless we're at max iterations
    const isComplete = agentSaysComplete && (hasActualResults || iteration >= maxIterations - 1)

    // Log completion decision for debugging
    if (agentSaysComplete && !hasActualResults && iteration < maxIterations - 1) {
      console.log(`[MCP-AGENT] 🔄 Agent wants to complete but no actual results yet - continuing (iteration ${iteration}/${maxIterations})`)
      console.log(`[MCP-AGENT] 🔍 Available tool results: ${progressSteps.filter(s => s.toolResult).map(s => s.toolResult?.content.substring(0, 50)).join(', ')}`)
    }

    if (isComplete) {
      // No tools to execute or agent explicitly says it's done
      // Check if we have meaningful tool results that actually answer the user's question
      const meaningfulToolResults = progressSteps
        .filter(step => step.toolResult?.success && step.toolResult.content)
        .map(step => step.toolResult!.content)
        .filter(content => content.trim().length > 10)

      // Check if any tool result contains actual data (not just session creation)
      const hasActualData = meaningfulToolResults.some(result => {
        const lowerResult = result.toLowerCase()
        return (
          // Contains process information
          lowerResult.includes('cpu') ||
          lowerResult.includes('%') ||
          lowerResult.includes('pid') ||
          lowerResult.includes('command') ||
          // Contains email data
          lowerResult.includes('from:') ||
          lowerResult.includes('subject:') ||
          // Contains file content
          (lowerResult.includes('file') && !lowerResult.includes('created')) ||
          // Contains numeric results
          /\d+\s+(files?|items?|emails?|results?)/.test(lowerResult) ||
          // Contains actual command output
          lowerResult.includes('processes') ||
          lowerResult.includes('memory') ||
          lowerResult.includes('usage')
        )
      })

      if (hasActualData) {
        // Use the most recent meaningful tool result that contains actual data
        const dataResult = meaningfulToolResults.reverse().find(result => {
          const lowerResult = result.toLowerCase()
          return (
            lowerResult.includes('cpu') ||
            lowerResult.includes('%') ||
            lowerResult.includes('pid') ||
            lowerResult.includes('command') ||
            lowerResult.includes('from:') ||
            lowerResult.includes('subject:') ||
            /\d+\s+(files?|items?|emails?|results?)/.test(lowerResult) ||
            lowerResult.includes('processes') ||
            lowerResult.includes('memory')
          )
        })

        // Extract and format the actual answer from tool results
        if (dataResult) {
          finalContent = extractAnswerFromToolResult(dataResult, transcript)
        } else {
          finalContent = meaningfulToolResults[0] || "Task completed with data results"
        }
        console.log(`[MCP-AGENT] 📋 Using meaningful data result as final content: ${finalContent.substring(0, 100)}...`)
      } else if (meaningfulToolResults.length > 0) {
        // Only use session/setup results if we have no actual data
        // But this suggests the task isn't really complete
        console.log(`[MCP-AGENT] ⚠️ Agent claims completion but only has setup results, not actual data`)
        finalContent = `Task in progress: ${meaningfulToolResults[meaningfulToolResults.length - 1]}`
      } else {
        finalContent = llmResponse.content || ""
        console.log(`[MCP-AGENT] 📋 Using LLM response as final content: ${finalContent.substring(0, 100)}...`)
      }

      conversationHistory.push({
        role: "assistant",
        content: finalContent
      })

      // Add completion step
      const completionStep = createProgressStep(
        "completion",
        "Task completed",
        "Successfully completed the requested task",
        "completed"
      )
      progressSteps.push(completionStep)

      // Emit final progress
      emitAgentProgress({
        currentIteration: iteration,
        maxIterations,
        steps: progressSteps.slice(-3),
        isComplete: true,
        finalContent
      })

      console.log(`[MCP-AGENT] ✅ Agent completed task in ${iteration} iterations`)
      break
    }

    // Execute tool calls with enhanced error handling
    if (!llmResponse.toolCalls || llmResponse.toolCalls.length === 0) {
      console.log(`[MCP-AGENT] ⚠️ No tool calls to execute, but task not marked complete. Ending iteration.`)
      // If we have no tool calls but the task isn't complete, we should still end the loop
      // to prevent infinite iterations
      finalContent = llmResponse.content || "Task completed without tool execution"
      conversationHistory.push({
        role: "assistant",
        content: finalContent
      })

      // Add completion step
      const completionStep = createProgressStep(
        "completion",
        "Task completed",
        "Completed without additional tool execution",
        "completed"
      )
      progressSteps.push(completionStep)

      // Emit final progress
      emitAgentProgress({
        currentIteration: iteration,
        maxIterations,
        steps: progressSteps.slice(-3),
        isComplete: true,
        finalContent
      })

      console.log(`[MCP-AGENT] ✅ Agent completed task in ${iteration} iterations (no tool calls)`)
      break
    }

    console.log(`[MCP-AGENT] 🔧 Executing ${llmResponse.toolCalls.length} tool calls`)
    const toolResults: MCPToolResult[] = []
    const failedTools: string[] = []

    for (const toolCall of llmResponse.toolCalls) {
      console.log(`[MCP-AGENT] Executing tool: ${toolCall.name}`)

      // Generate user-friendly tool description (with timeout and fallback)
      const getToolDescription = (toolName: string, args: any): string => {
        // For now, use intelligent fallback instead of LLM calls to avoid blocking
        // TODO: Implement async LLM calls with proper timeout handling

        // Extract meaningful information from tool name and arguments
        const cleanToolName = toolName.replace(/^[a-z]+_/, '').replace(/_/g, ' ')

        // Look for common argument patterns
        if (args.command) {
          if (Array.isArray(args.command)) {
            return `Running: ${args.command.join(' ')}`
          } else {
            return `Running: ${args.command}`
          }
        }

        if (args.query) {
          return `Searching for: ${args.query}`
        }

        if (args.email || args.subject) {
          return `Processing email: ${args.subject || args.email}`
        }

        if (args.file || args.path) {
          return `Working with file: ${args.file || args.path}`
        }

        if (args.url) {
          return `Accessing: ${args.url}`
        }

        // For tools with simple operations, use the tool name
        const actionWords = ['create', 'get', 'list', 'send', 'fetch', 'search', 'execute', 'run']
        const hasAction = actionWords.some(action => cleanToolName.includes(action))

        if (hasAction) {
          return `${cleanToolName.charAt(0).toUpperCase() + cleanToolName.slice(1)}`
        }

        // Fallback: show tool name with key arguments
        const keyArgs = Object.entries(args)
          .filter(([key, value]) => value && typeof value === 'string' && value.length < 50)
          .slice(0, 2)
          .map(([key, value]) => `${key}: ${value}`)
          .join(', ')

        return keyArgs ? `${cleanToolName}: ${keyArgs}` : `Executing ${cleanToolName}`
      }

      // Get user-friendly description
      const toolDescription = getToolDescription(toolCall.name, toolCall.arguments)

      // Add tool call step
      const toolCallStep = createProgressStep(
        "tool_call",
        `Executing ${toolCall.name}`,
        toolDescription,
        "in_progress"
      )
      toolCallStep.toolCall = {
        name: toolCall.name,
        arguments: toolCall.arguments
      }
      progressSteps.push(toolCallStep)

      // Emit progress update
      emitAgentProgress({
        currentIteration: iteration,
        maxIterations,
        steps: progressSteps.slice(-3),
        isComplete: false
      })

      // Execute tool with retry logic for transient failures
      let result = await executeToolCall(toolCall)
      let retryCount = 0
      const maxRetries = 2

      // Enhanced retry logic for specific error types
      while (result.isError && retryCount < maxRetries) {
        const errorText = result.content.map(c => c.text).join(' ').toLowerCase()

        // Check if this is a retryable error
        const isRetryableError =
          errorText.includes('timeout') ||
          errorText.includes('connection') ||
          errorText.includes('network') ||
          errorText.includes('temporary') ||
          errorText.includes('busy') ||
          errorText.includes('session not found') // Add session errors as retryable

        if (isRetryableError) {
          retryCount++
          console.log(`[MCP-AGENT] 🔄 Retrying tool ${toolCall.name} (attempt ${retryCount}/${maxRetries})`)

          // Special handling for resource-related errors
          if (errorText.includes('not found') || errorText.includes('invalid') || errorText.includes('expired')) {
            console.log(`[MCP-AGENT] 🔧 Resource error detected, attempting recovery`)

            // The retry mechanism will benefit from the updated context extraction
            // which will provide the correct resource IDs from conversation history
            console.log(`[MCP-AGENT] 🔄 Resource error detected, relying on context extraction for recovery`)
          }

          // Wait before retry (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000))

          result = await executeToolCall(toolCall)
        } else {
          break // Don't retry non-transient errors
        }
      }

      toolResults.push(result)

      // Track failed tools for better error reporting
      if (result.isError) {
        failedTools.push(toolCall.name)
      }

      // Context is now extracted from conversation history, no need to track manually

      // Update tool call step with result
      toolCallStep.status = result.isError ? "error" : "completed"
      toolCallStep.toolResult = {
        success: !result.isError,
        content: result.content.map(c => c.text).join('\n'),
        error: result.isError ? result.content.map(c => c.text).join('\n') : undefined
      }

      // Add tool result step with enhanced error information
      const toolResultStep = createProgressStep(
        "tool_result",
        `${toolCall.name} ${result.isError ? 'failed' : 'completed'}`,
        result.isError
          ? `Tool execution failed${retryCount > 0 ? ` after ${retryCount} retries` : ''}`
          : 'Tool executed successfully',
        result.isError ? "error" : "completed"
      )
      toolResultStep.toolResult = toolCallStep.toolResult
      progressSteps.push(toolResultStep)

      // Emit progress update
      emitAgentProgress({
        currentIteration: iteration,
        maxIterations,
        steps: progressSteps.slice(-3),
        isComplete: false
      })
    }

    // Add assistant response and tool results to conversation
    conversationHistory.push({
      role: "assistant",
      content: llmResponse.content || "",
      toolCalls: llmResponse.toolCalls!
    })

    const toolResultsText = toolResults.map(result =>
      result.content.map(c => c.text).join('\n')
    ).join('\n\n')

    conversationHistory.push({
      role: "tool",
      content: toolResultsText,
      toolResults
    })

    // Enhanced completion detection with better error handling
    const hasErrors = toolResults.some(result => result.isError)
    const allToolsSuccessful = toolResults.length > 0 && !hasErrors

    if (hasErrors) {
      console.log(`[MCP-AGENT] ⚠️ Tool execution had errors: ${failedTools.join(', ')}`)

      // Enhanced error analysis and recovery suggestions
      const errorAnalysis = analyzeToolErrors(toolResults, failedTools, llmResponse.toolCalls || [])

      // Add detailed error summary to conversation history for LLM context
      const errorSummary = `Tool execution errors occurred:
${failedTools.map(toolName => {
  const failedResult = toolResults.find(r => r.isError)
  const errorText = failedResult?.content.map(c => c.text).join(' ') || 'Unknown error'

  // Check for specific error patterns and suggest fixes
  let suggestion = ''
  if (errorText.includes('Session not found')) {
    suggestion = ' (Suggestion: Create a new session using ht_create_session first)'
  } else if (errorText.includes('timeout') || errorText.includes('connection')) {
    suggestion = ' (Suggestion: Retry the operation or check server connectivity)'
  } else if (errorText.includes('permission') || errorText.includes('access')) {
    suggestion = ' (Suggestion: Check file permissions or use alternative approach)'
  } else if (errorText.includes('command not found') || errorText.includes('invalid option')) {
    suggestion = ' (Suggestion: Try alternative commands like "ps aux" or "htop" for process information)'
  }

  return `- ${toolName}: ${errorText}${suggestion}`
}).join('\n')}

IMPORTANT: You must retry with working commands to get the actual data the user requested. Do not give up without providing the real answer.

${errorAnalysis.recoveryStrategy}

IMPORTANT: If you see session-related errors, you MUST create a new session first using ht_create_session,
then extract the session ID from the response and use it in subsequent tool calls.

Please try alternative approaches or provide manual instructions to the user.`

      conversationHistory.push({
        role: "tool",
        content: errorSummary
      })
    }

    // Check if agent indicated it was done after executing tools
    const agentIndicatedDone = (llmResponse as any).needsMoreWork === false

    if (agentIndicatedDone && allToolsSuccessful) {
      console.log(`[MCP-AGENT] 🎯 Agent indicated task completion and tools executed successfully`)

      // Create final content that includes tool results
      const toolResultsSummary = toolResults
        .filter(result => !result.isError)
        .map(result => result.content.map(c => c.text).join('\n'))
        .join('\n\n')

      // Use tool results if they contain meaningful content, otherwise fall back to LLM response
      if (toolResultsSummary && toolResultsSummary.trim().length > 10) {
        finalContent = toolResultsSummary || "Task completed with tool results"
        console.log(`[MCP-AGENT] 📋 Using tool results as final content: ${finalContent.substring(0, 100)}...`)
      } else {
        finalContent = llmResponse.content || ""
        console.log(`[MCP-AGENT] 📋 Using LLM response as final content: ${finalContent.substring(0, 100)}...`)
      }

      // Add completion step
      const completionStep = createProgressStep(
        "completion",
        "Task completed",
        "Successfully completed the requested task with tool results",
        "completed"
      )
      progressSteps.push(completionStep)

      // Emit final progress
      emitAgentProgress({
        currentIteration: iteration,
        maxIterations,
        steps: progressSteps.slice(-3),
        isComplete: true,
        finalContent
      })

      console.log(`[MCP-AGENT] ✅ Agent completed task in ${iteration} iterations`)
      break
    }

    // Check for completion keywords in the response
    const completionKeywords = ['completed', 'finished', 'done', 'success', 'created successfully', 'task complete']
    const responseText = (llmResponse.content || "").toLowerCase()
    const hasCompletionKeywords = completionKeywords.some(keyword => responseText.includes(keyword))

    if (allToolsSuccessful && hasCompletionKeywords) {
      console.log(`[MCP-AGENT] 🎯 Detected task completion signals - tools successful and completion keywords found`)
    }

    // Set final content to the latest assistant response (fallback)
    if (!finalContent) {
      finalContent = llmResponse.content || ""
    }
  }

  if (iteration >= maxIterations) {
    console.log(`[MCP-AGENT] ⚠️ Agent reached maximum iterations (${maxIterations})`)

    // Provide better feedback based on what happened
    const hasRecentErrors = progressSteps.slice(-5).some(step => step.status === "error")
    const errorMessage = hasRecentErrors
      ? "\n\n(Note: Task incomplete due to repeated tool failures. Please try again or use alternative methods.)"
      : "\n\n(Note: Task may not be fully complete - reached maximum iteration limit. The agent was still working on the request.)"

    finalContent += errorMessage

    // Add timeout completion step with better context
    const timeoutStep = createProgressStep(
      "completion",
      "Maximum iterations reached",
      hasRecentErrors
        ? "Task stopped due to repeated tool failures"
        : "Task stopped due to iteration limit",
      "error"
    )
    progressSteps.push(timeoutStep)

    // Emit final progress
    emitAgentProgress({
      currentIteration: iteration,
      maxIterations,
      steps: progressSteps.slice(-3),
      isComplete: true,
      finalContent
    })
  }

  return {
    content: finalContent || "Agent processing completed",
    conversationHistory,
    totalIterations: iteration
  }
}

async function makeLLMCall(messages: Array<{role: string, content: string}>, config: any): Promise<LLMToolCallResponse> {
  const chatProviderId = config.mcpToolsProviderId

  if (chatProviderId === "gemini") {
    const geminiModel = config.mcpToolsGeminiModel || "gemini-1.5-flash-002"
    console.log("[MCP-LLM-DEBUG] 🤖 Using Gemini model:", geminiModel)
    if (!config.geminiApiKey) throw new Error("Gemini API key is required")

    const gai = new GoogleGenerativeAI(config.geminiApiKey)
    const gModel = gai.getGenerativeModel({ model: geminiModel })

    const prompt = messages.map(m => `${m.role}: ${m.content}`).join('\n\n')

    console.log("[MCP-LLM-DEBUG] 📤 Sending request to Gemini...")
    try {
      const result = await gModel.generateContent([prompt], {
        baseUrl: config.geminiBaseUrl,
      })

      const responseText = result.response.text().trim()
      console.log("[MCP-LLM-DEBUG] 📥 Raw Gemini response:", responseText)

      // For Gemini, we still need to parse manually since it doesn't support structured output
      try {
        const parsed = JSON.parse(responseText)
        if (parsed && (parsed.toolCalls || parsed.content)) {
          console.log("[MCP-LLM-DEBUG] ✅ Parsed JSON:", JSON.stringify(parsed, null, 2))
          return parsed
        }
      } catch (parseError) {
        console.log("[MCP-LLM-DEBUG] ⚠️ Failed to parse Gemini response as JSON")
        diagnosticsService.logWarning('llm', 'Failed to parse Gemini response as JSON', { responseText })
      }
      return { content: responseText }
    } catch (error) {
      diagnosticsService.logError('llm', 'Gemini API call failed', error)
      throw error
    }
  }

  // Use structured output for OpenAI/Groq providers
  try {
    console.log("[MCP-LLM-DEBUG] 🚀 Using structured output for agent mode")
    const result = await makeStructuredToolCall(messages, chatProviderId)
    console.log("[MCP-LLM-DEBUG] ✅ Structured output successful:", JSON.stringify(result, null, 2))
    return result
  } catch (error) {
    console.error("[MCP-LLM-DEBUG] ❌ Structured output failed:", error)
    throw error
  }
}
